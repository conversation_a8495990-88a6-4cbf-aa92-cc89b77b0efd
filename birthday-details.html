<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>Luxury Birthday Celebrations - BharatX Events | Indian Theme Birthday Parties</title>
  <meta name="description" content="Luxury birthday party decorations with Indian themes, Bollywood parties, traditional celebrations, and modern setups. Premium birthday event planning in Delhi NCR.">
  <meta name="keywords" content="luxury birthday party, indian birthday celebration, bollywood theme party, traditional birthday decor, birthday event planning, delhi ncr, premium birthday decoration">

  <!-- Favicons -->
  <link href="assets/img/logo.png" rel="icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link
    href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Rubik:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
    rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">
  <link href="assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">
  <style></style>

</head>

<body class="service-details-page">

<header id="header" class="header d-flex align-items-center fixed-top">
    <div class="container-fluid container-xl position-relative d-flex align-items-center justify-content-between">

      <a href="index.html" class="logo d-flex align-items-center">
        <img src="assets/img/logo.png" class="logo-image" alt="" loading="lazy">
      </a>

      <nav id="navmenu" class="navmenu">
        <ul>
          <li><a href="index.html" class="active">Home</a></li>
          <li><a href="about.html">About</a></li>
          <li class="dropdown"><a href="services.html"><span>Services</span> <i class="bi bi-chevron-down dropdown-indicator"></i></a>
            <ul>
              <li><a href="birthday-details.html">Luxury Decor</a></li>
              <li><a href="photography-videography.html">Photography & Videography</a></li>
              <li><a href="fog-pyro-entry.html">Fog Entry & Pyro Entry</a></li>
              <li><a href="baby-cart-entry.html">Baby Cart Entry</a></li>
              <li><a href="mirror-ramp-entry.html">Mirror Ramp Entry</a></li>
              <li><a href="return-gift-hampers.html">Return Gift Hampers</a></li>
              <li><a href="destination-service.html">Destination Service</a></li>
              <li><a href="corporate-events.html">Corporate Events</a></li>
              <li><a href="wedding-details.html">Wedding & Engagement</a></li>
              <li><a href="naming-ceremony-details.html">Naming Ceremony</a></li>
            </ul>
          </li>
          <li><a href="gallery.html">Gallery</a></li>
          <li><a href="contact.html">Contact</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

      <a class="btn-getstarted" href="contact.html">Book Event</a>

    </div>
  </header>

  <main class="main">

    <!-- Page Title -->
    <div class="page-title dark-background" style="background-image: url('https://images.unsplash.com/photo-1530103862676-de8c9debad1d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');">
      <div class="container position-relative">
        <h1><i class="bi bi-balloon-heart-fill me-3" style="color: #ff6b35;"></i>Luxury Birthday Celebrations</h1>
        <p>Creating unforgettable birthday celebrations with Indian elegance and modern luxury.</p>
        <nav class="breadcrumbs">
          <ol>
            <li><a href="index.html">Home</a></li>
            <li><a href="services.html">Services</a></li>
            <li class="current">Luxury Birthday Celebrations</li>
          </ol>
        </nav>
      </div>
    </div><!-- End Page Title -->

    <!-- Service Details Section -->
    <section id="service-details" class="service-details section">

      <div class="container">

        <div class="row gy-5">

          <div class="col-lg-8">
            <div class="service-details-content indian-pattern">
              <!-- Hero Image with Badge -->
              <div class="service-hero-image position-relative mb-4">
                <img src="https://images.unsplash.com/photo-1530103862676-de8c9debad1d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
                     class="img-fluid rounded-3 shadow" alt="Luxury Birthday Party Decoration" loading="lazy">
                <div class="service-badge position-absolute top-0 end-0 m-3">
                  <span class="badge bg-primary fs-6 px-3 py-2">
                    <i class="bi bi-award-fill me-1"></i>Luxury Service
                  </span>
                </div>
              </div>

              <div class="service-content">
                <h2 class="mb-4 text-primary">Transforming Birthdays into Royal Celebrations</h2>

                <p class="lead mb-4">Every birthday is a milestone worth celebrating in grand style! At BharatX Events, we specialize in creating luxury birthday celebrations that blend Indian cultural richness with contemporary elegance. From intimate family gatherings to grand Bollywood-themed parties, we make every birthday unforgettable.</p>

                <!-- Feature Cards Row -->
                <div class="row mb-5">
                  <div class="col-md-6">
                    <div class="feature-card h-100 p-4 bg-light rounded-3">
                      <i class="bi bi-palette-fill text-primary fs-1 mb-3"></i>
                      <h4>Themed Celebrations</h4>
                      <p>Bollywood, Royal Rajasthani, Modern Chic, or Traditional Indian themes tailored to your preferences.</p>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="feature-card h-100 p-4 bg-light rounded-3">
                      <i class="bi bi-camera-fill text-primary fs-1 mb-3"></i>
                      <h4>Instagram-Worthy Setup</h4>
                      <p>Picture-perfect backdrops and photo booths that create lasting memories for social media.</p>
                    </div>
                  </div>
                </div>

                <!-- Service Highlights -->
                <div class="service-highlights">
                  <h4><i class="bi bi-star-fill"></i>Our Birthday Specialties</h4>
                  <div class="row">
                    <div class="col-md-6">
                      <ul class="list-unstyled">
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Bollywood Theme Parties</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Royal Rajasthani Decor</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Traditional Indian Celebrations</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Modern Luxury Themes</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Custom Balloon Installations</li>
                      </ul>
                    </div>
                    <div class="col-md-6">
                      <ul class="list-unstyled">
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>LED & Lighting Effects</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Photo Booth & Props</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Cake Table Decoration</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Entertainment Coordination</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>Return Gift Arrangements</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- Popular Themes Section -->
                <div class="mt-5">
                  <h3 class="mb-4">Popular Birthday Themes</h3>
                  <div class="row">
                    <div class="col-md-4 mb-3">
                      <div class="theme-card p-3 border rounded-3 h-100">
                        <h5 class="text-primary"><i class="bi bi-film me-2"></i>Bollywood Bash</h5>
                        <p class="mb-0">Glamorous Bollywood-themed parties with movie posters, red carpets, and filmi music.</p>
                      </div>
                    </div>
                    <div class="col-md-4 mb-3">
                      <div class="theme-card p-3 border rounded-3 h-100">
                        <h5 class="text-primary"><i class="bi bi-gem me-2"></i>Royal Rajasthani</h5>
                        <p class="mb-0">Majestic royal themes with traditional Rajasthani decor, mirrors, and rich fabrics.</p>
                      </div>
                    </div>
                    <div class="col-md-4 mb-3">
                      <div class="theme-card p-3 border rounded-3 h-100">
                        <h5 class="text-primary"><i class="bi bi-flower1 me-2"></i>Garden Party</h5>
                        <p class="mb-0">Fresh floral arrangements with natural elements and outdoor elegance.</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Process Steps -->
                <div class="process-steps">
                  <h3 class="mb-4">Our Birthday Planning Process</h3>
                  <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                      <h5>Theme Selection & Consultation</h5>
                      <p>We discuss your preferences, age group, and desired theme to create the perfect celebration concept.</p>
                    </div>
                  </div>
                  <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                      <h5>Custom Design & Planning</h5>
                      <p>Our creative team designs a unique setup with mood boards, color schemes, and decoration layouts.</p>
                    </div>
                  </div>
                  <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                      <h5>Setup & Celebration Management</h5>
                      <p>Complete setup, coordination with vendors, and on-site management for a stress-free celebration.</p>
                    </div>
                  </div>
                </div>

                <p class="mt-4">From milestone birthdays to intimate family celebrations, we create magical moments that reflect your personality and style. Our team handles every detail, allowing you to focus on enjoying your special day with loved ones.</p>

              </div>
            </div>
          </div>

          <!-- Sidebar -->
          <div class="col-lg-4">
            <div class="service-sidebar">
              <h4>Service Information</h4>
              <div class="service-info-item">
                <i class="bi bi-clock"></i>
                <span>Setup Time: 3-5 hours</span>
              </div>
              <div class="service-info-item">
                <i class="bi bi-people"></i>
                <span>Capacity: 20-300 guests</span>
              </div>
              <div class="service-info-item">
                <i class="bi bi-calendar-event"></i>
                <span>Advance Booking: 1 week</span>
              </div>
              <div class="service-info-item">
                <i class="bi bi-geo-alt"></i>
                <span>Service Area: Delhi NCR</span>
              </div>
              <div class="service-info-item">
                <i class="bi bi-currency-rupee"></i>
                <span>Starting from ₹25,000</span>
              </div>

              <!-- Popular Add-ons -->
              <div class="mt-4 p-3 bg-light rounded-3">
                <h5 class="mb-3">Popular Add-ons</h5>
                <ul class="list-unstyled mb-0">
                  <li class="mb-2"><i class="bi bi-plus-circle text-primary me-2"></i>DJ & Music System</li>
                  <li class="mb-2"><i class="bi bi-plus-circle text-primary me-2"></i>Photography Package</li>
                  <li class="mb-2"><i class="bi bi-plus-circle text-primary me-2"></i>Catering Services</li>
                  <li class="mb-2"><i class="bi bi-plus-circle text-primary me-2"></i>Return Gift Hampers</li>
                </ul>
              </div>

              <!-- Call to Action -->
              <div class="service-cta mt-4">
                <h3>Plan Your Dream Birthday!</h3>
                <p>Let's create an unforgettable celebration together!</p>
                <a href="contact.html" class="btn btn-light">Get Free Quote</a>
              </div>
            </div>
          </div>
        </div>

      </div>

    </section><!-- /Service Details Section -->

    <!-- Gallery Section -->
    <section class="service-gallery section py-5" style="background: #f8f9fa;">
      <div class="container">
        <div class="text-center mb-5">
          <h2 class="section-title">Birthday Celebration Gallery</h2>
          <p class="lead">Explore our stunning birthday party setups and decorations</p>
        </div>

        <!-- Gallery Filters -->
        <div class="gallery-filters">
          <button class="gallery-filter-btn active" data-filter="all">All</button>
          <button class="gallery-filter-btn" data-filter="bollywood">Bollywood Theme</button>
          <button class="gallery-filter-btn" data-filter="royal">Royal Theme</button>
          <button class="gallery-filter-btn" data-filter="modern">Modern Setup</button>
        </div>

        <div class="gallery-grid">
          <div class="gallery-item" data-category="modern">
            <img src="https://images.unsplash.com/photo-1530103862676-de8c9debad1d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                 alt="Luxury Birthday Setup" loading="lazy">
            <div class="overlay">
              <i class="bi bi-zoom-in"></i>
              <div class="gallery-title">Luxury Modern Setup</div>
              <div class="gallery-subtitle">Elegant contemporary design</div>
            </div>
          </div>
          <div class="gallery-item" data-category="bollywood">
            <img src="https://images.unsplash.com/photo-1464207687429-7505649dae38?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                 alt="Bollywood Theme Party" loading="lazy">
            <div class="overlay">
              <i class="bi bi-zoom-in"></i>
              <div class="gallery-title">Bollywood Bash</div>
              <div class="gallery-subtitle">Glamorous filmi celebration</div>
            </div>
          </div>
          <div class="gallery-item" data-category="modern">
            <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                 alt="Birthday Balloon Decoration" loading="lazy">
            <div class="overlay">
              <i class="bi bi-zoom-in"></i>
              <div class="gallery-title">Balloon Installations</div>
              <div class="gallery-subtitle">Creative balloon artistry</div>
            </div>
          </div>
          <div class="gallery-item" data-category="modern">
            <img src="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                 alt="Birthday Cake Table" loading="lazy">
            <div class="overlay">
              <i class="bi bi-zoom-in"></i>
              <div class="gallery-title">Cake Table Setup</div>
              <div class="gallery-subtitle">Instagram-worthy displays</div>
            </div>
          </div>
          <div class="gallery-item" data-category="royal">
            <img src="https://images.unsplash.com/photo-1519167758481-83f29c8d8d4f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                 alt="Royal Rajasthani Theme" loading="lazy">
            <div class="overlay">
              <i class="bi bi-zoom-in"></i>
              <div class="gallery-title">Royal Rajasthani</div>
              <div class="gallery-subtitle">Majestic traditional setup</div>
            </div>
          </div>
          <div class="gallery-item" data-category="bollywood">
            <img src="https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                 alt="Photo Booth Setup" loading="lazy">
            <div class="overlay">
              <i class="bi bi-zoom-in"></i>
              <div class="gallery-title">Photo Booth</div>
              <div class="gallery-subtitle">Fun memory-making corner</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonial Section -->
    <section class="py-5">
      <div class="container">
        <div class="service-testimonial">
          <p>"BharatX Events made my daughter's 16th birthday absolutely spectacular! The Bollywood theme was executed perfectly with amazing decorations, lighting, and even a red carpet entrance. All our guests were amazed by the attention to detail. It was truly a night to remember!"</p>
          <div class="client-info">
            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                 alt="Client" class="client-avatar">
            <div class="client-details">
              <h5>Meera Gupta</h5>
              <span>Gurgaon, Mother of Ananya</span>
            </div>
          </div>
        </div>
      </div>
    </section>

  </main>

   <footer id="footer" class="footer position-relative dark-background">

    <div class="container footer-top">
      <div class="row gy-4">
        <div class="col-lg-4 col-md-6 footer-about">
          <a href="index.html" class="logo d-flex align-items-center">
            <span class="sitename">BharatX Events</span>
          </a>
          <div class="footer-contact pt-3">
            <p>No matter the occasion—birthday, engagement, wedding, naming ceremony, or corporate event—BharatX Events
              is here to make it truly special.
            </p>
            <p>Nx one, tower t3, b-107, Noida Extention</p>
            <p class="mt-3"><strong>Phone:</strong><a href="tel:9310804776" class="mt-3"><span> +91-9310804776</span></a>
            </p>
            <p><strong>Email:</strong><a href="mailto:<EMAIL>" class="mt-3">
                <span> <EMAIL></span></a> </p>
          </div>
          <!-- <div class="social-links d-flex mt-4">
            <a href=""><i class="bi bi-twitter-x"></i></a>
            <a href=""><i class="bi bi-facebook"></i></a>
            <a href=""><i class="bi bi-instagram"></i></a>
            <a href=""><i class="bi bi-linkedin"></i></a>
          </div> -->
        </div>

        <div class="col-lg-2 col-md-3 footer-links">
          <h4>Useful Links</h4>
          <ul>
            <li><a href="index.html">Home</a></li>
            <li><a href="about.html">About us</a></li>
            <li><a href="services.html">Services</a></li>
            <li><a href="terms.html">Terms of service</a></li>
            <li><a href="privacy.html">Privacy policy</a></li>
          </ul>
        </div>

        <div class="col-lg-2 col-md-3 footer-links">
          <h4>Our Services</h4>
          <ul>
            <li><a href="birthday-details.html">Luxury Decor</a></li>
            <li><a href="photography-videography.html">Photography & Videography</a></li>
            <li><a href="fog-pyro-entry.html">Fog Entry & Pyro Entry</a></li>
            <li><a href="baby-cart-entry.html">Baby Cart Entry</a></li>
            <li><a href="mirror-ramp-entry.html">Mirror Ramp Entry</a></li>
            <li><a href="return-gift-hampers.html">Return Gift Hampers</a></li>
            <li><a href="destination-service.html">Destination Service</a></li>
            <li><a href="corporate-events.html">Corporate Events</a></li>
            <li><a href="wedding-details.html">Wedding & Engagement</a></li>
            <li><a href="naming-ceremony-details.html">Naming Ceremony</a></li>
          </ul>
        </div>

        <div class="col-lg-4 col-md-6 footer-links">
          <h4>Our Location</h4>
          <iframe width="100%" height="300" style="border:0" loading="lazy" allowfullscreen=""
            referrerpolicy="no-referrer-when-downgrade"
            src="https://www.google.com/maps/embed?pb=!1m17!1m12!1m3!1d3502.329318205344!2d77.*************!3d28.60098448488446!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m2!1m1!2zMjjCsDM2JzAzLjUiTiA3N8KwMjYnMDMuOCJF!5e0!3m2!1sen!2sin!4v1725363223789!5m2!1sen!2sin"></iframe>
        </div>

        <div class="container copyright text-center mt-4">
          <p>© <span>Copyright</span>
            <strong class="px-1 sitename text-warning">BharatX Events</strong>
            <span>All Rights Reserved</span>
          </p>
          <div class="credits">
            Designed by <a href="https://www.bitspark.in/"><img class="copyright-img"
                src="https://www.bitspark.in/wp-content/uploads/2024/12/logo-white3.png" alt="Bit Spark Logo" loading="lazy"></a>
          </div>
        </div>

  </footer>

  <!-- Scroll Top -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i
      class="bi bi-arrow-up-short"></i></a>

  <!-- Preloader -->
  <div id="preloader"></div>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/php-email-form/validate.js"></script>
  <script src="assets/vendor/purecounter/purecounter_vanilla.js"></script>
  <script src="assets/vendor/swiper/swiper-bundle.min.js"></script>
  <script src="assets/vendor/glightbox/js/glightbox.min.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

  <!-- Gallery Filter Script -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Gallery filter functionality
      const filterBtns = document.querySelectorAll('.gallery-filter-btn');
      const galleryItems = document.querySelectorAll('.gallery-item');

      filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          const filter = this.getAttribute('data-filter');

          // Update active button
          filterBtns.forEach(b => b.classList.remove('active'));
          this.classList.add('active');

          // Filter gallery items with animation
          galleryItems.forEach(item => {
            if (filter === 'all' || item.getAttribute('data-category') === filter) {
              item.style.display = 'block';
              item.style.opacity = '0';
              item.style.transform = 'translateY(20px)';

              setTimeout(() => {
                item.style.transition = 'all 0.5s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
              }, 100);
            } else {
              item.style.transition = 'all 0.3s ease';
              item.style.opacity = '0';
              item.style.transform = 'translateY(-20px)';

              setTimeout(() => {
                item.style.display = 'none';
              }, 300);
            }
          });
        });
      });

      // Gallery modal functionality
      const galleryItemsModal = document.querySelectorAll('.gallery-item');
      galleryItemsModal.forEach(item => {
        item.addEventListener('click', function() {
          const img = this.querySelector('img');
          const title = this.querySelector('.gallery-title')?.textContent || 'Gallery Image';

          // Create modal if it doesn't exist
          let modal = document.querySelector('.gallery-modal');
          if (!modal) {
            modal = document.createElement('div');
            modal.className = 'gallery-modal';
            modal.innerHTML = `
              <div class="gallery-modal-content">
                <span class="gallery-modal-close">&times;</span>
                <img src="" alt="">
              </div>
            `;
            document.body.appendChild(modal);

            // Close modal functionality
            modal.querySelector('.gallery-modal-close').addEventListener('click', () => {
              modal.classList.remove('active');
            });

            modal.addEventListener('click', (e) => {
              if (e.target === modal) {
                modal.classList.remove('active');
              }
            });
          }

          // Update modal content and show
          const modalImg = modal.querySelector('img');
          modalImg.src = img.src;
          modalImg.alt = title;
          modal.classList.add('active');
        });
      });
    });
  </script>

</body>

</html>